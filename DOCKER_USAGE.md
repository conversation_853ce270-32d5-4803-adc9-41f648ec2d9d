# Docker 使用說明

## 快速開始

### 1. 使用 Docker Compose（推薦）

```bash
# 構建並啟動服務
docker-compose up --build

# 在背景執行
docker-compose up -d --build

# 停止服務
docker-compose down

# 查看日誌
docker-compose logs -f jetbrains-proxy
```

### 2. 使用 Docker 命令

```bash
# 構建映像
docker build -t jetbrains-proxy .

# 執行容器（Web UI 模式）
docker run -d \
  --name jetbrains-proxy \
  -p 8501:8501 \
  -v $(pwd)/config.json:/app/config.json:ro \
  -v $(pwd)/jetbrainsai.json:/app/jetbrainsai.json \
  -v $(pwd)/logs:/app/logs \
  jetbrains-proxy

# 執行容器（命令行模式）
docker run --rm \
  -v $(pwd)/config.json:/app/config.json:ro \
  jetbrains-proxy \
  python main.py --list
```

## 端口說明

- **8501**: Streamlit Web UI 介面
- **8000**: 外部服務端口（如果需要）

## 卷掛載說明

- `./config.json:/app/config.json:ro` - 配置文件（只讀）
- `./jetbrainsai.json:/app/jetbrainsai.json` - JetBrains AI 配置文件
- `./logs:/app/logs` - 日誌目錄

## 環境變數

可以通過環境變數自定義行為：

```bash
docker run -e PYTHONUNBUFFERED=1 jetbrains-proxy
```

## 常用命令

### 查看容器狀態
```bash
docker ps
docker-compose ps
```

### 進入容器
```bash
docker exec -it jetbrains-proxy bash
```

### 查看日誌
```bash
docker logs jetbrains-proxy
docker-compose logs jetbrains-proxy
```

### 重啟服務
```bash
docker-compose restart jetbrains-proxy
```

## 故障排除

### 1. 端口衝突
如果端口 8501 已被佔用，可以修改 docker-compose.yml：
```yaml
ports:
  - "8502:8501"  # 使用 8502 端口
```

### 2. 配置文件問題
確保 config.json 文件存在且格式正確：
```bash
# 檢查配置文件
cat config.json | python -m json.tool
```

### 3. 權限問題
```bash
# 修正文件權限
chmod 644 config.json
chmod 755 logs/
```

## 生產環境部署

對於生產環境，建議：

1. 使用具體的映像標籤而非 `latest`
2. 設定適當的資源限制
3. 配置日誌輪轉
4. 使用 secrets 管理敏感信息

```yaml
# docker-compose.prod.yml 範例
version: '3.8'
services:
  jetbrains-proxy:
    image: jetbrains-proxy:v1.0.0
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```
