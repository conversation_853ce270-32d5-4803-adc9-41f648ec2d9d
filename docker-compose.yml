services:
  jetbrains-proxy:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jetbrains-proxy
    ports:
      - "8501:8501"
    volumes:
      - ./config.json:/app/config.json:ro
      - ./jetbrainsai.json:/app/jetbrainsai.json
      - ./logs:/app/logs
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 如果您需要外部服務，可以取消註釋以下部分
  # external-service:
  #   image: your-external-service-image
  #   container_name: jetbrains-external-service
  #   ports:
  #     - "8000:8000"
  #   volumes:
  #     - ./jetbrainsai.json:/app/jetbrainsai.json:ro
  #   depends_on:
  #     - jetbrains-proxy
  #   restart: unless-stopped

networks:
  default:
    name: jetbrains-network
